import hashlib
import json
from pathlib import Path
from typing import Any, <PERSON><PERSON>, TypeVar

from pydantic import BaseModel, Field, ValidationError
from rich.console import Console

console = Console()

T = TypeVar("T", bound=BaseModel)


# --- Deprecated Models (for transition) ---


class AnalysisInput(BaseModel):
    """DEPRECATED: Represents a single input file and its content hash."""

    path: str
    hash: str


class AnalysisMetadata(BaseModel):
    """DEPRECATED: Holds metadata about the analysis, including all input files."""

    inputs: list[AnalysisInput]


def calculate_file_hash(file_path: Path) -> str | None:
    """Calculates the SHA256 hash of a file.

    Returns the hex digest of the hash, or None if the file cannot be read.
    """
    if not file_path.exists() or not file_path.is_file():
        return None
    try:
        hasher = hashlib.sha256()
        with file_path.open("rb") as f:
            while chunk := f.read(8192):
                hasher.update(chunk)
        return hasher.hexdigest()
    except OSError as e:
        console.print(f"  [red]Could not hash file {file_path}: {e}[/red]")
        return None


class Metadata(BaseModel):
    """Metadata about the inputs used to generate the analysis."""

    input_files: dict[str, str] = Field(
        ...,
        description="Maps input file paths to their content hashes.",
    )
    params: dict[str, Any] | None = Field(
        default=None,
        description="Free-form parameters used in the analysis (e.g., model versions).",
    )


class IdempotencyWrapper(BaseModel, Generic[T]):
    """A generic wrapper for storing analysis results with idempotency checks."""

    metadata: Metadata
    result: T


def load_and_check_idempotency(
    output_path: Path,
    model_class: type[T],
    input_files: list[Path] | None = None,
    params: dict[str, Any] | None = None,
    force: bool = False,
) -> T | None:
    """Loads an existing idempotency file and checks if it's stale.

    Returns the validated result model if the cache is valid, otherwise None.
    """
    if force:
        console.print(f"  [bold yellow]Forcing re-analysis for {output_path.name}.[/bold yellow]")
        return None

    if not output_path.exists():
        return None

    console.print(
        f"  Found existing analysis at [cyan]{output_path.name}[/cyan]. Checking for staleness...",
    )

    # 1. Load and validate the file structure
    try:
        # We need to construct the concrete generic type for Pydantic
        concrete_wrapper = IdempotencyWrapper[model_class]
        try:
            # Try to load the new format with 'metadata' first
            existing_wrapper = concrete_wrapper.model_validate_json(
                output_path.read_text("utf-8"),
            )
        except (ValidationError, KeyError) as e:
            console.print(
                f"[yellow]Could not validate existing analysis file {output_path.name}: {e}. Re-running.[/yellow]",
            )
            try:
                output_path.unlink()
                console.print(f"  [yellow]Deleted corrupted file: {output_path.name}[/yellow]")
            except OSError as unlink_error:
                console.print(f"[red]Error deleting corrupted file {output_path.name}: {unlink_error}[/red]")
                return None
    except (ValidationError, json.JSONDecodeError) as e:
        console.print(
            f"[yellow]Could not validate existing analysis file {output_path.name}: {e}. Re-running.[/yellow]",
        )
        try:
            output_path.unlink()
            console.print(f"  [yellow]Deleted corrupted file: {output_path.name}[/yellow]")
        except OSError as unlink_error:
            console.print(f"[red]Error deleting corrupted file {output_path.name}: {unlink_error}[/red]")
        return None

    # 2. Check if parameters have changed
    if existing_wrapper.metadata.params != params:
        console.print(
            f"  [yellow]Parameters have changed for {output_path.name}, re-running analysis.[/yellow]",
        )
        return None

    # 3. Check if input files have changed
    if input_files:
        current_hashes = {
            str(p.resolve()): h
            for p in input_files
            if p.exists() and (h := calculate_file_hash(p))
        }
        if current_hashes != existing_wrapper.metadata.input_files:
            console.print(
                f"  [yellow]Input files have changed for {output_path.name}, re-running analysis.[/yellow]",
            )
            return None

    console.print(f"  [green]Analysis for {output_path.name} is up-to-date.[/green]")
    return existing_wrapper.result


def save_idempotent_result(
    output_path: Path,
    result: BaseModel,
    input_files: list[Path] | None = None,
    params: dict[str, Any] | None = None,
) -> None:
    """Saves a result to a file with idempotency information."""
    console.print(f"  Saving result to [cyan]{output_path.name}[/cyan]...")
    hashes: dict[str, str] = {}
    if input_files:
        hashes = {
            str(p.resolve()): h
            for p in input_files
            if p.exists() and (h := calculate_file_hash(p))
        }
    info = Metadata(input_files=hashes, params=params)
    wrapper = IdempotencyWrapper(metadata=info, result=result)

    output_path.parent.mkdir(parents=True, exist_ok=True)
    with output_path.open("w", encoding="utf-8") as f:
        f.write(wrapper.model_dump_json(indent=2))

    console.print(f"  [green]Successfully saved {output_path.name}.[/green]")
