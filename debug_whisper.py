#!/usr/bin/env python3
"""Debug script to test Whisper pipeline output format."""

from pathlib import Path

import librosa
from transformers import pipeline

from src.utils import get_device


def debug_whisper():
    # Test with a small audio sample first
    vocals_path = Path("output/mr._nightmare/3 Disturbing TRUE Lighthouse Horror Stories/separation/htdemucs/rBAiuCqJF1Y/vocals.wav")

    if not vocals_path.exists():
        print(f"File not found: {vocals_path}")
        return

    print(f"Testing with file: {vocals_path}")

    # Load the same pipeline as the main app
    device = get_device()
    print(f"Using device: {device}")

    transcription_pipeline = pipeline(
        "automatic-speech-recognition",
        model="openai/whisper-large-v3",
        device=device,
        torch_dtype="auto",
    )

    # Load just the first 10 seconds to test quickly
    audio_input, sample_rate = librosa.load(vocals_path, sr=16000, duration=10)
    print(f"Audio loaded: {len(audio_input)} samples at {sample_rate}Hz")

    # Test the simple pipeline call first
    print("\n=== TESTING SIMPLE PIPELINE CALL ===")
    try:
        simple_result = transcription_pipeline(audio_input, return_timestamps="word")
        print(f"Simple result type: {type(simple_result)}")
        print(f"Simple result keys: {list(simple_result.keys()) if isinstance(simple_result, dict) else 'Not a dict'}")

        if isinstance(simple_result, dict) and "chunks" in simple_result:
            chunks = simple_result["chunks"]
            print(f"Chunks type: {type(chunks)}")
            print(f"Number of chunks: {len(chunks) if isinstance(chunks, list) else 'Not a list'}")

            if isinstance(chunks, list) and len(chunks) > 0:
                print(f"First chunk: {chunks[0]}")
                print(f"First chunk type: {type(chunks[0])}")
                if isinstance(chunks[0], dict):
                    print(f"First chunk keys: {list(chunks[0].keys())}")
                    for key, value in chunks[0].items():
                        print(f"  {key}: {type(value)} = {value}")

                if len(chunks) > 1:
                    print(f"Second chunk: {chunks[1]}")

        print(f"Full simple result: {simple_result}")

    except Exception as e:
        print(f"Simple pipeline failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_whisper()
