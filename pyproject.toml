[project]
name = "story_time"
version = "0.1.0"
requires-python = ">=3.12"
dependencies = [
    "torch==2.6.0",
    "torchvision==0.21.0",
    "torchaudio==2.6.0",
    "xformers",
    # "flash-attn @ git+https://github.com/Dao-AILab/flash-attention.git@v2.7.4",
    "yt-dlp>=2025.6.30",
    "typer>=0.16.0",
    "rich>=14.0.0",
    "insanely-fast-whisper>=0.0.15",
    "transformers>=4.53.0",
    "accelerate>=1.8.1",
    "python-ffmpeg>=2.0.12",
    "pydantic>=2.11.7",
    "ImageHash>=4.3.1",
    "Pillow>=10.5.0",
    "triton-windows<3.3",
    "librosa>=0.10.1",
    "demucs>=4.0.1",
    "audonnx>=0.3.0",
    "audeer>=2.0.0",
    "onnxruntime-gpu>=1.18.0",
    "modelscope[audio]>=1.16.0",
    "soundfile>=0.13.1",
    "addict>=2.4.0",
    "pysptk",
    "opencv-python>=4.11.0.86",
    "sentence-transformers>=5.0.0",
    "pyannote.audio>=3.1.1",
]

[tool.uv]
cache-dir = ".cache"
# find-links = ["https://modelscope.oss-cn-beijing.aliyuncs.com/releases/repo.html"]
dev-dependencies = [
    "ruff>=0.12.1",
    "ruff-lsp>=0.0.62",
    "pyright>=1.1.402",
    "pre-commit>=3.8.0",
    "pytest>=8.2.2",
    "pytest-env>=1.1.3",
    "pytest-xdist[psutil]>=3.6.1",
    "pytest-freezegun>=0.4.2",
    "pytest-asyncio>=0.24.0",
    "ninja>=1.11.1.4",
    "wheel>=0.45.1",
    "setuptools>=69.5.1",
]
no-build-isolation-package = ["flash-attn"]


[tool.uv.sources]
torch = [
    { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]
torchvision = [
    { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]
torchaudio = [
    { index = "pytorch-cu124", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]
pysptk = { git = "https://github.com/r9y9/pysptk" }


[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true

[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[tool.slotscheck]
strict-imports = false

[tool.codespell]
ignore-words-list = "alog"
skip = 'package-lock.json'

[tool.pytest_env]
ENVIRONMENT = "unit_test"
