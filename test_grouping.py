#!/usr/bin/env python3
"""Test script to test the _group_words_into_segments function."""

from src.analysis.transcription import _group_words_into_segments

def test_grouping():
    # Test with the problematic format we saw in the debug output
    test_chunks = [
        {'text': '.', 'timestamp': (9.7, None)},
        {'text': ' Hello', 'timestamp': (10.0, 10.5)},
        {'text': ' world', 'timestamp': (10.5, 11.0)},
        {'text': '.', 'timestamp': (11.0, None)},
    ]
    
    print("Testing _group_words_into_segments with problematic chunks:")
    print(f"Input chunks: {test_chunks}")
    
    try:
        segments = _group_words_into_segments(test_chunks)
        print(f"Success! Generated {len(segments)} segments:")
        for i, segment in enumerate(segments):
            print(f"  Segment {i+1}: {segment}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_grouping()
