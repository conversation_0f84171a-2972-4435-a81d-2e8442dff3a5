from __future__ import annotations

from datetime import date
from pathlib import Path
from typing import Any

import yt_dlp
from pydantic import BaseModel
from rich.console import Console

console = Console()


class VideoInfo(BaseModel):
    """A Pydantic model to hold video information."""

    id: str | None = None
    url: str | None = None
    title: str | None = None
    author: str | None = None
    upload_date: date | None = None
    view_count: int | None = None


def find_videos_recursively(data: Any) -> list[dict[str, Any]]:
    """Recursively find all video entries in the data structure."""
    videos: list[dict[str, Any]] = []
    if isinstance(data, dict):
        # Check if it's a video entry.
        if data.get("id") and data.get("url") and data.get("_type") != "playlist":
            videos.append(data)

        # Recursively search in 'entries'.
        entries = data.get("entries")
        if isinstance(entries, list):
            for item in entries:
                videos.extend(find_videos_recursively(item))

    elif isinstance(data, list):
        for item in data:
            videos.extend(find_videos_recursively(item))

    return videos


def get_top_videos(
    channel_url: str, top_n: int, proxy: str | None = None,
) -> tuple[str | None, list[VideoInfo]]:
    """Fetches top N videos from a YouTube channel based on view count."""
    ydl_opts: dict[str, Any] = {
        "extract_flat": True,
        "force_generic_extractor": True,
        "playlist_sort": "view_count",
        "playlistend": top_n,
    }
    videos: list[VideoInfo] = []
    channel_title: str | None = None
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            if not info:
                console.print(
                    "[red]Error: Could not extract video information.[/red]",
                )
                return None, []

            channel_title = info.get("title")
            all_video_entries = find_videos_recursively(info)

            valid_videos = [
                v for v in all_video_entries if v.get("view_count") is not None
            ]
            sorted_videos = sorted(
                valid_videos,
                key=lambda x: x.get("view_count", 0),
                reverse=True,
            )
            top_videos_data = sorted_videos[:top_n]

            for v in top_videos_data:
                upload_date_str = v.get("upload_date")
                upload_date = (
                    date.fromisoformat(upload_date_str)
                    if upload_date_str and len(upload_date_str) == 8
                    else None
                )
                video_id = v.get("id")
                video_url = v.get("webpage_url")
                if video_id and not video_url:
                    video_url = f"https://www.youtube.com/watch?v={video_id}"

                videos.append(
                    VideoInfo(
                        id=video_id,
                        url=video_url,
                        title=v.get("title"),
                        author=v.get("uploader"),
                        upload_date=upload_date,
                        view_count=v.get("view_count"),
                    ),
                )
        except Exception as e:
            console.print(f"[red]An error occurred during video discovery: {e}[/red]")

    return channel_title, videos


def download_video(video: VideoInfo, video_path: Path) -> bool:
    """Downloads a single video to the specified path."""
    if video_path.exists():
        console.print(f"Video '{video.title}' already exists. Skipping download.")
        return True

    console.print(f"Downloading '{video.title}'...")
    ydl_opts: dict[str, Any] = {
        "format": "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
        "outtmpl": str(video_path),
    }
    if not video.url:
        console.print(f"[red]No URL for video '{video.title}', cannot download.[/red]")
        return False

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([video.url])
        return True
    except Exception as e:
        console.print(f"[red]Error downloading video '{video.title}': {e}[/red]")
        return False
