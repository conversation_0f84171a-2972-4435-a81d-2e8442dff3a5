from __future__ import annotations

import time

import torch
from rich.console import Console

console = Console()


def human_readable_duration(duration_seconds: float) -> str:
    """Converts a duration in seconds to a human-readable string (h, m, s, ms)."""
    hours = int(duration_seconds // 3600)
    minutes = int((duration_seconds % 3600) // 60)
    seconds = int(duration_seconds % 60)
    milliseconds = int((duration_seconds % 1) * 1000)

    parts: list[str] = []
    if hours:
        parts.append(f"{hours}h")
    if minutes:
        parts.append(f"{minutes}m")
    if seconds:
        parts.append(f"{seconds}s")
    if milliseconds:
        parts.append(f"{milliseconds}ms")

    return " ".join(parts) if parts else "0s"


def get_device() -> str:
    """Checks for CUDA availability and returns the appropriate device."""
    if torch.cuda.is_available():
        console.print("  [green]CUDA is available. Using GPU.[/green]")
        return "cuda"
    console.print("  [yellow]CUDA not available. Using CPU.[/yellow]")
    return "cpu"


def log_step_duration(start_time: float, step_name: str) -> None:
    """Logs the duration of a processing step."""
    duration = time.time() - start_time
    console.print(f"  [dim]'{step_name}' took {human_readable_duration(duration)}[/dim]")

