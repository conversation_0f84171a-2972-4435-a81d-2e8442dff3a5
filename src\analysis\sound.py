import time
from pathlib import Path

import soundfile as sf
from pydantic import BaseModel
from rich.console import Console
from rich.progress import track
from transformers.pipelines import pipeline

from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.utils import get_device, log_step_duration

console = Console()


# --- Pydantic Models for Sound Effect Analysis ---


class SoundEffectPrediction(BaseModel):
    """Represents a single prediction from the audio classification model."""

    score: float
    label: str


class RawChunkAnalysis(BaseModel):
    """Represents the raw analysis output for a single audio chunk."""

    start_time: float
    predictions: list[SoundEffectPrediction]


class RawSoundEffectAnalysis(BaseModel):
    """Represents the full raw output from the sound effect model."""

    chunks: list[RawChunkAnalysis]


class SoundEffectEvent(BaseModel):
    """Represents a single sound effect event in the timeline."""

    start: float
    end: float
    label: str


class SoundEffectAnalysis(BaseModel):
    """Represents the analysis results for sound effect detection."""

    events: list[SoundEffectEvent]


# --- Main Analysis Functions ---


def analyze_sound_effects(
    audio_path: Path,
    output_path: Path,
    force: bool = False,
    model_name: str = "MIT/ast-finetuned-audioset-10-10-0.4593",
    top_k: int = 5,
    chunk_length_s: float = 5.0,
    hop_length_s: float = 1.0,
) -> SoundEffectAnalysis | None:
    """Analyzes an audio file to detect sound effects using a two-step process.

    Step 1: Raw Analysis (Idempotent)
        - Chunks the audio and runs a classification model on each chunk.
        - Saves the raw list of predictions for all chunks to `..._raw.json`.

    Step 2: Processing (Idempotent)
        - Loads the raw predictions.
        - Filters out low-confidence or irrelevant sounds (e.g., speech).
        - Creates events from the first valid prediction in each chunk.
        - Merges consecutive events with the same label.
        - Saves the final event list to the specified `output_path`.

    Args:
        audio_path: Path to the audio file.
        output_path: Path to save the final processed JSON analysis result.
        force: If True, re-run all analysis steps even if cached results exist.
        model_name: The name of the Hugging Face model to use.
        top_k: The number of top predictions to consider for each chunk.
        chunk_length_s: The duration of each audio chunk for analysis.
        hop_length_s: The step size to slide the analysis window.

    Returns:
        A SoundEffectAnalysis object, or None on error.
    """
    console.print(f"Analyzing sound effects for [cyan]{audio_path.name}[/cyan]...")
    overall_start_time = time.time()

    raw_output_path = output_path.with_name(f"{output_path.stem}_raw.json")

    # --- Step 1: Get Raw Classification from Model (Idempotent) ---
    raw_params = {
        "model": model_name,
        "top_k": top_k,
        "chunk_length_s": chunk_length_s,
        "hop_length_s": hop_length_s,
    }
    raw_input_files = [audio_path]

    raw_result = load_and_check_idempotency(
        output_path=raw_output_path,
        model_class=RawSoundEffectAnalysis,
        input_files=raw_input_files,
        params=raw_params,
        force=force,
    )

    if not raw_result:
        console.print(f"Running sound effect classification on [cyan]{audio_path.name}[/cyan]...")
        inference_start_time = time.time()
        try:
            device = get_device()
            pipe = pipeline(
                "audio-classification",
                model=model_name,
                device=0 if device == "cuda" else -1,
            )

            audio_data, sample_rate = sf.read(audio_path, dtype="float32")
            if audio_data.ndim > 1:  # To mono
                audio_data = audio_data.mean(axis=1)

            chunk_length_samples = int(chunk_length_s * sample_rate)
            hop_length_samples = int(hop_length_s * sample_rate)
            num_chunks = (
                (len(audio_data) - chunk_length_samples) // hop_length_samples + 1
                if len(audio_data) >= chunk_length_samples
                else 1
            )

            raw_chunks_data = []
            for i in track(range(num_chunks), description="Analyzing audio chunks..."):
                start_sample = i * hop_length_samples
                end_sample = start_sample + chunk_length_samples
                chunk = audio_data[start_sample:end_sample]
                predictions = pipe(
                    {"sampling_rate": sample_rate, "raw": chunk}, top_k=top_k,
                )
                raw_chunks_data.append(
                    {
                        "start_time": start_sample / sample_rate,
                        "predictions": predictions or [],
                    },
                )

            raw_result = RawSoundEffectAnalysis.model_validate({"chunks": raw_chunks_data})
            save_idempotent_result(
                output_path=raw_output_path,
                result=raw_result,
                input_files=raw_input_files,
                params=raw_params,
            )
            log_step_duration(inference_start_time, "Sound effect inference")
        except Exception as e:
            console.print(f"[red]Error during sound effect analysis: {e}[/red]")
            return None

    # --- Step 2: Process Raw Classification (Idempotent) ---
    processing_params = {}
    processing_input_files = [raw_output_path]

    final_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=SoundEffectAnalysis,
        input_files=processing_input_files,
        params=processing_params,
        force=force,
    )

    if final_result:
        return final_result

    console.print("Processing raw sound effect classification...")
    sfx_events: list[SoundEffectEvent] = []
    for chunk_analysis in raw_result.chunks:
        for prediction in chunk_analysis.predictions:
            if prediction.score < 0.2:
                continue
            if any(w in prediction.label.lower() for w in ["speech", "music", "inside", "outside"]):
                continue

            sfx_events.append(
                SoundEffectEvent(
                    start=chunk_analysis.start_time,
                    end=chunk_analysis.start_time + hop_length_s,
                    label=prediction.label,
                ),
            )
            break  # Take first valid prediction per chunk

    # Merge consecutive events with the same label
    merged_events = []
    if sfx_events:
        sfx_events.sort(key=lambda x: x.start)
        current_event = sfx_events[0]
        for next_event in sfx_events[1:]:
            if (
                next_event.label == current_event.label
                and (next_event.start - current_event.end) < 0.5
            ):
                current_event.end = next_event.end
            else:
                merged_events.append(current_event)
                current_event = next_event
        merged_events.append(current_event)

    final_result = SoundEffectAnalysis(events=merged_events)
    save_idempotent_result(
        output_path=output_path,
        result=final_result,
        input_files=processing_input_files,
        params=processing_params,
    )

    console.print(
        f"[green]Sound effect analysis complete. Found {len(merged_events)} events. "
        f"Result saved to {output_path.name}[/green]",
    )
    log_step_duration(overall_start_time, "Total sound effect analysis")
    return final_result




