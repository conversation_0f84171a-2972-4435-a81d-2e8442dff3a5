.env
.env_old
ngrok_auth_token.txt
output/
*.log
**/*/__pycache__/**/*
db.sqlite3
*.pem
*/**/__pycache__
__pycache__
state_of_the_union.txt
*.secret
*.history
*.out
leads_guru/*.out
leads_guru/static
history
leads_guru/nohup.out
debug.log
.venv
test_data
.DS_Store
venv
.pytest_cache
.idea/dataSources.xml
node_modules
.env.debug
.env.dev
.env.debug
.aider*
.cache
.ruff_cache
config.secrets.yml
aider*
dist
*.py~
