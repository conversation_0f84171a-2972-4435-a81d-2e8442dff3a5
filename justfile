alias install := sync

process youtube_url="https://www.youtube.com/@mrnightmare" top_n="1":
    uv run python -m src.main {{youtube_url}} --top-n {{top_n}}

# Install python dependencies
sync:
    uv sync

upgrade:
    uv sync --upgrade

# Install pre-commit hooks
pre_commit_setup:
    uv run pre-commit sync

# Install python dependencies and pre-commit hooks
setup: sync pre_commit_setup

# Run pre-commit
pre_commit:
    uv run pre-commit run -a

# Run pytest
test:
    uv run pytest tests

lint folder="." fix="":
    ruff check {{folder}} {{fix}}

pyright directory=".":
    pyright --threads 8 {{directory}}

clean-analysis:
    find ./output -type f -name "*.json" ! -name "transcript.json" -exec rm -f {} \;
