from typing import Any

import transformers


class WhisperForAudioCaptioning(transformers.WhisperForConditionalGeneration):
    """A wrapper for WhisperForConditionalGeneration to support forced_ac_decoder_ids."""

    def __init__(self, config: Any):
        super().__init__(config)

    def generate(self, *args: Any, **kwargs: Any) -> Any:
        """Overrides generate to handle forced_ac_decoder_ids."""
        if "forced_ac_decoder_ids" in kwargs:
            forced_ac_decoder_ids = kwargs.pop("forced_ac_decoder_ids")
            kwargs["forced_decoder_ids"] = forced_ac_decoder_ids

        return super().generate(*args, **kwargs)

