#!/usr/bin/env python3
"""Test script to test just the transcription functionality."""

from pathlib import Path
from transformers import pipeline
from src.utils import get_device
from src.analysis.transcription import analyze_transcription

def test_transcription():
    # Test with the vocals.wav file
    vocals_path = Path("output/mr._nightmare/3 Disturbing TRUE Lighthouse Horror Stories/separation/htdemucs/rBAiuCqJF1Y/vocals.wav")
    output_path = Path("test_transcript.json")
    
    if not vocals_path.exists():
        print(f"File not found: {vocals_path}")
        return
    
    print(f"Testing transcription with file: {vocals_path}")
    
    # Load the same pipeline as the main app
    device = get_device()
    print(f"Using device: {device}")
    
    transcription_pipeline = pipeline(
        "automatic-speech-recognition",
        model="openai/whisper-large-v3",
        device=device,
        torch_dtype="auto",
    )
    
    print("Pipeline loaded successfully.")
    
    # Test the transcription function
    try:
        result = analyze_transcription(
            speech_path=vocals_path,
            output_path=output_path,
            transcription_pipeline=transcription_pipeline,
            force=True,  # Force re-run to test the fix
        )
        
        if result:
            print(f"Transcription successful!")
            print(f"Language: {result.language}")
            print(f"Text: {result.text[:200]}...")  # First 200 chars
            print(f"Number of segments: {len(result.segments)}")
            if result.segments:
                print(f"First segment: {result.segments[0]}")
        else:
            print("Transcription failed - returned None")
            
    except Exception as e:
        print(f"Transcription failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transcription()
