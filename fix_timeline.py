#!/usr/bin/env python3
"""Fix the timeline generation from current transcription data."""

import json
from pathlib import Path


def fix_timeline():
    # Paths
    video_dir = Path("output/mr._nightmare/3 Disturbing TRUE Lighthouse Horror Stories")
    transcript_path = video_dir / "transcription.json"
    timeline_path = video_dir / "timeline_fixed.json"

    if not transcript_path.exists():
        print(f"Transcription file not found: {transcript_path}")
        return

    print(f"Loading transcription from: {transcript_path}")

    # Load transcription data
    with transcript_path.open("r", encoding="utf-8") as f:
        transcript_data = json.load(f)

    # Extract segments from the current format
    if "result" in transcript_data and "segments" in transcript_data["result"]:
        segments = transcript_data["result"]["segments"]
        print(f"Found {len(segments)} segments")
    else:
        print("No segments found in transcription")
        return

    # Create timeline events from segments
    timeline_events = []
    for segment in segments:
        timeline_events.append({
            "type": "transcript",
            "start_time": segment["start"],
            "end_time": segment["end"],
            "text": segment["text"],
        })

    # Sort events by start time
    timeline_events.sort(key=lambda x: x["start_time"])

    # Save the timeline
    print(f"Saving timeline with {len(timeline_events)} events to: {timeline_path}")
    print(f"First event: {timeline_events[0] if timeline_events else 'None'}")

    # Write to a temporary file first to debug
    temp_path = timeline_path.with_suffix(".tmp")
    with temp_path.open("w", encoding="utf-8") as f:
        json.dump(timeline_events, f, indent=2)

    print(f"Wrote to temp file: {temp_path}")
    print(f"Temp file size: {temp_path.stat().st_size} bytes")

    # Now copy to final location
    import shutil
    shutil.move(str(temp_path), str(timeline_path))
    print(f"Moved to final location: {timeline_path}")
    print(f"Final file size: {timeline_path.stat().st_size} bytes")

    print("Timeline generation complete!")
    print(f"Timeline has {len(timeline_events)} events")
    if timeline_events:
        print(f"Duration: {timeline_events[0]['start_time']:.1f}s to {timeline_events[-1]['end_time']:.1f}s")

        # Show first few segments to check quality
        print("\nFirst 3 segments:")
        for i, event in enumerate(timeline_events[:3]):
            print(f"  {i+1}. [{event['start_time']:.1f}s - {event['end_time']:.1f}s] {event['text']}")

if __name__ == "__main__":
    fix_timeline()
