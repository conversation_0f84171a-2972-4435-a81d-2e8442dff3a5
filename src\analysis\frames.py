import json
import re
import time
from functools import lru_cache
from pathlib import Path

import cv2
import imagehash
import numpy as np
import torch
from PIL import Image
from pydantic import BaseModel
from rich.console import Console
from rich.progress import track
from transformers import AutoProcessor, LlavaForConditionalGeneration

from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.utils import get_device, log_step_duration

console = Console()


# --- Pydantic Models for Frame Analysis ---


class UniqueFrame(BaseModel):
    """Represents a single unique frame selected for description."""

    frame_path: str
    sharpness: float
    timestamp: float


class FrameAnalysisEvent(BaseModel):
    """Represents a single unique frame event in the timeline."""

    timestamp: float
    image_hash: str


class UniqueImage(BaseModel):
    """Represents a unique image identified from the video frames."""

    description: str
    path: str


class FrameAnalysis(BaseModel):
    """Represents the analysis results for video frames."""

    unique_images: dict[str, UniqueImage]
    events: list[FrameAnalysisEvent]


class UniqueFramesAnalysis(BaseModel):
    """The result of the unique frame selection analysis."""

    unique_frames: list[UniqueFrame]


class DescribedFrame(BaseModel):
    """Represents a single frame with its generated description."""

    frame_path: str
    description: str
    timestamp: float


class FrameDescriptionAnalysis(BaseModel):
    """The result of the frame description analysis step."""

    described_frames: list[DescribedFrame]


# --- Frame Analysis Helpers ---


@lru_cache(maxsize=1)
def get_llava_pipeline() -> tuple[LlavaForConditionalGeneration | None, AutoProcessor | None, str]:
    """Gets the Llava model and processor, caching it using lru_cache."""
    console.print("  Loading Llava pipeline...")
    device = get_device()
    # This is a large model, so we handle potential memory issues
    model_id = "llava-hf/llava-1.5-7b-hf"
    pipeline = None
    processor = None
    try:
        processor = AutoProcessor.from_pretrained(model_id, use_fast=True)
        model = LlavaForConditionalGeneration.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            low_cpu_mem_usage=True,
        )
        pipeline = model.to(device)
    except Exception as e:
        console.print(f"[red]Failed to load Llava model '{model_id}': {e}[/red]")

    return pipeline, processor, device


# --- Main Analysis Functions ---


def analyze_frame_descriptions(
    unique_frames_path: Path,
    output_path: Path,
    prompt: str = "Describe this image in a concise sentence.",
    force: bool = False,
) -> FrameDescriptionAnalysis | None:
    """Generates descriptions for unique frames using Llava.

    Args:
        unique_frames_path: Path to the unique frames analysis file.
        output_path: Path to save the description analysis file.
        prompt: The prompt to use for the Llava model.
        force: If True, re-run the analysis even if a cached result exists.
    """
    console.print(f"Analyzing frame descriptions for [cyan]{unique_frames_path.name}[/cyan]...")
    overall_start_time = time.time()

    # 1. Idempotency check
    params = {"prompt": prompt, "model": "llava-hf/llava-1.5-7b-hf"}
    input_files = [unique_frames_path]

    cached_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=FrameDescriptionAnalysis,
        input_files=input_files,
        params=params,
        force=force,
    )
    if cached_result:
        return cached_result

    # 2. Load unique frames from the previous step
    try:
        data = json.loads(unique_frames_path.read_text(encoding="utf-8"))
        unique_frames_result = UniqueFramesAnalysis.model_validate(data["result"])
    except (KeyError, json.JSONDecodeError, Exception) as e:
        console.print(f"[red]Could not load or parse unique frames file {unique_frames_path}: {e}[/red]")
        return None

    # 3. Load model
    model_load_start = time.time()
    llava_pipeline, llava_processor, device = get_llava_pipeline()
    if not llava_pipeline or not llava_processor:
        console.print("[red]Llava pipeline not available. Skipping description.[/red]")
        return None
    log_step_duration(model_load_start, "Llava model loading")

    # 4. Generate descriptions
    description_start = time.time()
    described_frames = []
    for frame in track(
        unique_frames_result.unique_frames,
        description="Describing frames...",
    ):
        try:
            image = Image.open(frame.frame_path)
            full_prompt = f"USER: <image>\n{prompt}\nASSISTANT:"
            inputs = llava_processor(
                text=full_prompt,
                images=image,
                return_tensors="pt",
            ).to(device)

            generate_ids = llava_pipeline.generate(**inputs, max_new_tokens=75)
            output = llava_processor.batch_decode(
                generate_ids,
                skip_special_tokens=True,
                clean_up_tokenization_spaces=False,
            )[0]

            # Clean up the output, removing the prompt
            assistant_response = output.split("ASSISTANT:")[-1].strip()

            described_frames.append(
                DescribedFrame(
                    frame_path=frame.frame_path,
                    description=assistant_response,
                    timestamp=frame.timestamp,
                ),
            )
        except Exception as e:
            console.print(
                f"[yellow]Could not describe frame {frame.frame_path}: {e}[/yellow]",
            )
            continue
    log_step_duration(description_start, "Frame description")

    # 5. Create and save result
    result = FrameDescriptionAnalysis(described_frames=described_frames)
    save_idempotent_result(
        output_path=output_path,
        result=result,
        input_files=input_files,
        params=params,
    )
    log_step_duration(overall_start_time, "Total frame description analysis")
    return result


def analyze_unique_frames(
    frame_paths: list[Path],
    output_path: Path,
    fps: float,
    hash_size: int = 16,
    sharpness_threshold: float = 20.0,
    force: bool = False,
) -> UniqueFramesAnalysis | None:
    """Analyzes a list of frames to find a unique set based on image hashing and sharpness.

    This function is idempotent. It performs several steps:
    1.  Checks for a cached result. If found and inputs/params are unchanged, returns it.
    2.  Filters out pure black or near-black frames.
    3.  Calculates a perceptual hash (phash) for each remaining frame.
    4.  Groups frames by identical hash.
    5.  For each group, it selects the sharpest frame as the representative.
    6.  Saves the result with idempotency info.

    Args:
        frame_paths: A list of paths to the frame images.
        output_path: The path to save the JSON analysis result.
        fps: The framerate of the video, used for accurate timestamp calculation.
        hash_size: The size of the perceptual hash.
        sharpness_threshold: The minimum sharpness score to consider.
        force: If True, re-run analysis even if a cached result exists.

    Returns:
        A UniqueFramesAnalysis object containing the list of unique frames, or None on error.
    """
    console.print(f"Analyzing {len(frame_paths)} frames for unique content...")
    overall_start_time = time.time()

    # 1. Idempotency Check
    params = {
        "hash_size": hash_size,
        "sharpness_threshold": sharpness_threshold,
        "fps": fps,
    }
    cached_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=UniqueFramesAnalysis,
        input_files=frame_paths,
        params=params,
        force=force,
    )
    if cached_result:
        console.print(f"Unique frames are up to date for [cyan]{output_path.name}[/cyan]. Skipping.")
        return cached_result

    # 2. Process frames: calculate hash and sharpness
    processing_start_time = time.time()
    processed_frames = []
    for frame_path in track(frame_paths, description="Processing frames..."):
        try:
            # Open with Pillow for hashing
            image = Image.open(frame_path)
            # Open with OpenCV for sharpness calculation
            cv_image = cv2.imread(str(frame_path))

            if cv_image is None:
                continue

            # Filter out black frames
            if np.max(cv_image) < 10:  # Heuristic for black frames
                continue

            # Calculate sharpness
            sharpness = cv2.Laplacian(cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY), cv2.CV_64F).var()

            # Calculate perceptual hash
            image_hash = imagehash.phash(image, hash_size=hash_size)

            # Extract timestamp from filename
            match = re.search(r"frame_(\d+)\.jpg$", frame_path.name)
            if match:
                frame_number = int(match.group(1))
                timestamp = frame_number / fps
                processed_frames.append(
                    {
                        "path": frame_path,
                        "hash": image_hash,
                        "sharpness": sharpness,
                        "timestamp": timestamp,
                    },
                )
        except Exception as e:
            console.print(f"[yellow]Could not process frame {frame_path}: {e}[/yellow]")
            continue
    log_step_duration(processing_start_time, "Frame processing (hash and sharpness)")

    if not processed_frames:
        console.print("[yellow]No suitable frames found after filtering.[/yellow]")
        result = UniqueFramesAnalysis(unique_frames=[])
        save_idempotent_result(
            output_path=output_path,
            result=result,
            input_files=frame_paths,
            params=params,
        )
        return result

    # 3. Group frames by identical hash
    grouping_start_time = time.time()
    frame_groups: dict[imagehash.ImageHash, list[dict]] = {}
    for frame in processed_frames:
        frame_hash = frame["hash"]
        if frame_hash not in frame_groups:
            frame_groups[frame_hash] = []
        frame_groups[frame_hash].append(frame)
    log_step_duration(grouping_start_time, "Grouping by identical hash")

    # 4. Select the sharpest frame from each group
    selection_start_time = time.time()
    unique_frames = []
    for frame_group in frame_groups.values():
        if frame_group:
            sharpest_frame = max(frame_group, key=lambda x: x["sharpness"])
            unique_frames.append(
                UniqueFrame(
                    frame_path=str(sharpest_frame["path"]),
                    sharpness=sharpest_frame["sharpness"],
                    timestamp=sharpest_frame["timestamp"],
                ),
            )

    # Sort frames by timestamp for consistent output
    unique_frames.sort(key=lambda x: x.timestamp)
    log_step_duration(selection_start_time, "Sharpest frame selection")

    # 5. Save the result
    result = UniqueFramesAnalysis(unique_frames=unique_frames)
    save_idempotent_result(
        output_path=output_path,
        result=result,
        input_files=frame_paths,
        params=params,
    )

    console.print(
        f"[green]Identified {len(unique_frames)} unique frames. Result saved to {output_path.name}[/green]",
    )
    log_step_duration(overall_start_time, "Total unique frame analysis")
    return result
