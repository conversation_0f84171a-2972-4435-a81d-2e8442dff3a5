import subprocess
import sys
from pathlib import Path

from rich.console import Console

console = Console()


def separate_audio_demucs(
    audio_path: Path, output_dir: Path, force: bool = False,
) -> tuple[Path | None, Path | None]:
    """Separates audio into speech and music tracks using Demucs.

    Args:
        audio_path: The path to the input audio file.
        output_dir: The directory to save the separated audio files.
        force: If True, re-run separation even if output files exist.

    Returns:
        A tuple containing the paths to the speech and music files, respectively.
        Returns (None, None) if separation fails.
    """
    console.log(f"Starting audio separation for [cyan]{audio_path.name}[/cyan] with Demucs...")

    # Demucs creates a subdirectory based on the model name in the output_dir.
    # We'll use the default 'htdemucs' model.
    model_name = "htdemucs"
    demucs_out_dir = output_dir / model_name / audio_path.stem

    speech_path = demucs_out_dir / "vocals.wav"
    music_path = demucs_out_dir / "no_vocals.wav"

    # --- Idempotency Check ---
    if not force and speech_path.exists() and music_path.exists():
        console.log(
            f"[yellow]Separated audio already exists for {audio_path.name}. Skipping.[/yellow]",
        )
        return speech_path, music_path

    # --- Demucs Subprocess Call ---
    # The command is simply `demucs "path/to/audio.mp3" -o "output/dir"`
    # The virtual environment's Python should be used, which has demucs installed.
    command = [
        sys.executable,  # Use the same python that is running the script
        "-m",
        "demucs",
        "--two-stems=vocals",
        "-o",
        str(output_dir),
        str(audio_path),
    ]

    try:
        console.log(f"Running command: {' '.join(command)}")
        process = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            encoding="utf-8",
            # Demucs can be slow, so a longer timeout might be needed for large files.
            timeout=600,
        )
        console.log(f"Demucs output:\n{process.stdout}")

        if speech_path.exists() and music_path.exists():
            console.log(
                f"[green]Successfully separated audio for {audio_path.name}[/green]",
            )
            return speech_path, music_path

        console.log(
            f"[red]Demucs command ran, but output files not found in {demucs_out_dir}[/red]",
        )
        console.log(f"Expected speech path: {speech_path}")
        console.log(f"Expected music path: {music_path}")
        return None, None

    except FileNotFoundError:
        console.log(
            "[bold red]Error: 'python' command not found.[/bold red]",
            "Please ensure Python from your virtual environment is in your system's PATH.",
        )
        return None, None
    except subprocess.TimeoutExpired:
        console.log(
            "[bold red]Demucs process timed out.[/bold red]",
            "The audio file may be too long or the system is under heavy load.",
        )
        return None, None
    except subprocess.CalledProcessError as e:
        console.log("[bold red]An error occurred during Demucs execution.[/bold red]")
        console.log(f"Return code: {e.returncode}")
        console.log(f"Stderr:\n{e.stderr}")
        if "torch.cuda.OutOfMemoryError" in e.stderr:
            console.log(
                "[bold yellow]Hint: CUDA out of memory.[/bold yellow]",
                "Try reducing the model complexity or processing shorter audio clips.",
            )
        return None, None
    except Exception as e:
        console.log(f"[bold red]An unexpected error occurred: {e}[/bold red]")
        return None, None
