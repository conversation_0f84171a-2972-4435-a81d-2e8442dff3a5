{"pythonVersion": "3.12", "venvPath": ".", "venv": ".venv", "typeCheckingMode": "standard", "deprecateTypingAliases": true, "reportCallInDefaultInitializer": "none", "reportImplicitOverride": "none", "reportImplicitStringConcatenation": "none", "reportImportCycles": "error", "reportUnknownVariableType": false, "reportMissingSuperCall": "none", "reportPropertyTypeMismatch": "error", "reportShadowedImports": "error", "reportUninitializedInstanceVariable": "error", "reportUnnecessaryTypeIgnoreComment": "error", "reportUnusedCallResult": "none", "include": ["src"], "extraPaths": ["src"]}