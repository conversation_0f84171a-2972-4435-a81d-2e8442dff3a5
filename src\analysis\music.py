import time
from pathlib import Path

import soundfile as sf
from pydantic import BaseModel
from rich.console import Console
from transformers.pipelines import pipeline

from src.idempotency import load_and_check_idempotency, save_idempotent_result
from src.utils import get_device, log_step_duration

console = Console()


# --- Pydantic Models for Music Analysis ---


class MusicPrediction(BaseModel):
    """Represents a single prediction from the audio classification model."""

    score: float
    label: str


class RawMusicAnalysis(BaseModel):
    """Represents the raw output from the music classification model."""

    predictions: list[MusicPrediction]


class MusicAnalysisEvent(BaseModel):
    """Represents a single music event in the timeline."""

    start: float
    end: float
    # The label is usually just "Music"
    label: str


class MusicAnalysis(BaseModel):
    """Represents the analysis results for music detection."""

    events: list[MusicAnalysisEvent]


# --- Main Analysis Functions ---


def analyze_music(
    audio_path: Path,
    output_path: Path,
    force: bool = False,
    model_name: str = "dima806/music_genres_classification",
) -> MusicAnalysis | None:
    """Analyzes an audio file for music using a two-step idempotent process.

    Step 1: Raw Classification (Idempotent)
        - Runs a genre classification model on the audio.
        - Saves the raw list of predictions to `..._raw.json`.

    Step 2: Processing (Idempotent)
        - Loads the raw predictions.
        - If any prediction is not 'speech', creates a single event spanning
          the full audio duration.
        - Saves the final event list to the specified `output_path`.

    Args:
        audio_path: Path to the audio file.
        output_path: Path to save the final processed JSON analysis result.
        force: If True, re-run all analysis steps even if cached results exist.
        model_name: The name of the Hugging Face model to use.

    Returns:
        A MusicAnalysis object, or None on error.
    """
    console.print(f"Analyzing music for [cyan]{audio_path.name}[/cyan]...")
    overall_start_time = time.time()

    raw_output_path = output_path.with_name(f"{output_path.stem}_raw.json")

    # --- Step 1: Get Raw Classification from Model (Idempotent) ---
    raw_params = {"model": model_name}
    raw_input_files = [audio_path]

    raw_result = load_and_check_idempotency(
        output_path=raw_output_path,
        model_class=RawMusicAnalysis,
        input_files=raw_input_files,
        params=raw_params,
        force=force,
    )

    if not raw_result:
        console.print(f"Running music classification on [cyan]{audio_path.name}[/cyan]...")
        inference_start_time = time.time()
        try:
            device = get_device()
            pipe = pipeline(
                "audio-classification",
                model=model_name,
                device=0 if device == "cuda" else -1,
            )
            predictions = pipe(str(audio_path), top_k=5)
            # Explicitly validate the raw dict list into our Pydantic model
            # to satisfy static type checkers and ensure data integrity.
            raw_result = RawMusicAnalysis.model_validate({"predictions": predictions})

            save_idempotent_result(
                output_path=raw_output_path,
                result=raw_result,
                input_files=raw_input_files,
                params=raw_params,
            )
            log_step_duration(inference_start_time, "Music classification inference")
        except Exception as e:
            console.print(f"[red]Error during music analysis: {e}[/red]")
            return None

    # --- Step 2: Process Raw Classification (Idempotent) ---
    processing_params = {}
    processing_input_files = [raw_output_path]

    final_result = load_and_check_idempotency(
        output_path=output_path,
        model_class=MusicAnalysis,
        input_files=processing_input_files,
        params=processing_params,
        force=force,
    )

    if final_result:
        return final_result

    console.print("Processing raw music classification...")
    music_events = []
    if raw_result and any(p.label.lower() != "speech" for p in raw_result.predictions):
        try:
            with sf.SoundFile(str(audio_path)) as f:
                duration = len(f) / f.samplerate
            music_events.append(MusicAnalysisEvent(start=0, end=duration, label="Music"))
        except ImportError:
            console.print(
                "[red]Soundfile library not found. Cannot determine audio duration. Please install it.[/red]",
            )
        except Exception as e:
            console.print(f"[red]Could not get audio duration: {e}[/red]")

    # Save the final processed result
    final_result = MusicAnalysis(events=music_events)
    save_idempotent_result(
        output_path=output_path,
        result=final_result,
        input_files=processing_input_files,
        params=processing_params,
    )

    console.print(
        f"[green]Music analysis complete. Found {len(music_events)} music events. "
        f"Result saved to {output_path.name}[/green]",
    )
    log_step_duration(overall_start_time, "Total music analysis")
    return final_result
